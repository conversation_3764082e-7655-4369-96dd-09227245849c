@testable import CrateServices
import Foundation
import SwiftData
import XCTest

class CollectionServiceTests: XCTestCase {
  // MARK: - Mocks

  class MockHTTPClient: HTTPClient {
    var mockResponse: Data?
    var mockError: Error?
    var capturedPath: String?
    var capturedBody: Data?
    var capturedParameters: Any?

    override init(baseURL: String = "http://localhost:8000") {
      super.init(baseURL: baseURL)
      setApiKey("crate2025")
    }

    // Override the correct method signature with parameters
    override public func get<Parameters: Encodable, Response: Decodable>(
      path: String,
      parameters: Parameters? = nil
    ) async throws -> Response {
      capturedPath = path
      capturedParameters = parameters

      if let error = mockError {
        throw error
      }

      guard let responseData = mockResponse else {
        throw HTTPError.invalidResponse
      }

      return try JSONDecoder().decode(Response.self, from: responseData)
    }

    override public func post<RequestBody: Encodable, Response: Decodable>(
      path: String,
      body: RequestBody? = nil
    ) async throws -> Response {
      capturedPath = path
      if let body = body {
        capturedBody = try? JSONEncoder().encode(body)
      }

      if let error = mockError {
        throw error
      }

      guard let responseData = mockResponse else {
        throw HTTPError.invalidResponse
      }

      return try JSONDecoder().decode(Response.self, from: responseData)
    }

    // Add override for delete method
    override public func delete<RequestBody: Encodable>(
      path: String,
      body: RequestBody? = nil
    ) async throws -> Data {
      capturedPath = path
      if let body = body {
        capturedBody = try? JSONEncoder().encode(body)
      }

      if let error = mockError {
        throw error
      }

      return mockResponse ?? Data()
    }
  }

  class MockUserState: UserState {
    var mockIsSignedIn = false
    var mockToken: String?

    override var isSignedIn: Bool {
      return mockIsSignedIn
    }

    override var token: String? {
      get { return mockToken }
      set { mockToken = newValue }
    }
  }

  // MARK: - Test Setup

  var mockClient: MockHTTPClient!
  var mockUserState: MockUserState!
  var collectionService: CollectionService!

  override func setUp() {
    super.setUp()
    mockClient = MockHTTPClient()
    mockUserState = MockUserState()
    collectionService = CollectionService(
      client: mockClient,
      userState: mockUserState
    )
  }

  override func tearDown() {
    mockClient = nil
    mockUserState = nil
    collectionService = nil
    super.tearDown()
  }

  // MARK: - Test Cases

  func testGetCollectionsSuccess() async {
    // Given
    mockUserState.mockIsSignedIn = true
    mockUserState.mockToken = "valid-token"

    let mockCollectionsJSON = """
    [
        {
            "id": 1,
            "name": "Test Collection",
            "thumbnail": "https://example.com/thumbnail.jpg",
            "tracks": [
                {
                    "id": 101,
                    "trackTitle": "Test Track",
                    "artistName": "Test Artist",
                    "url": "https://example.com/track",
                    "created": "2024-01-01T00:00:00Z",
                    "updated": "2024-01-01T00:00:00Z"
                }
            ],
            "created": "2024-01-01T00:00:00Z",
            "updated": "2024-01-01T00:00:00Z"
        }
    ]
    """
    mockClient.mockResponse = mockCollectionsJSON.data(using: .utf8)

    // When
    do {
      let collections = try await collectionService.getCollections()

      // Then
      XCTAssertEqual(mockClient.capturedPath, "/api/v1/collection")
      XCTAssertEqual(collections.count, 1)
      XCTAssertEqual(collections[0].name, "Test Collection")
      XCTAssertEqual(collections[0].tracks?.count ?? 0, 1)
      XCTAssertEqual(collections[0].tracks?[0].name, "Test Track")
      XCTAssertEqual(collections[0].tracks?[0].artist, "Test Artist")
    } catch {
      XCTFail("Get collections should succeed, but failed with error: \(error)")
    }
  }

  func testGetCollectionsWithCustomParameters() async {
    // Given
    mockUserState.mockIsSignedIn = true
    mockUserState.mockToken = "valid-token"

    let mockCollectionsJSON = """
    [
        {
            "id": 1,
            "name": "Test Collection",
            "thumbnail": "https://example.com/thumbnail.jpg",
            "tracks": [],
            "created": "2024-01-01T00:00:00Z",
            "updated": "2024-01-01T00:00:00Z"
        }
    ]
    """
    mockClient.mockResponse = mockCollectionsJSON.data(using: .utf8)

    // When
    do {
      let collections = try await collectionService.getCollections(start: 10, size: 5)

      // Then
      XCTAssertEqual(mockClient.capturedPath, "/api/v1/collection")
      XCTAssertEqual(collections.count, 1)
      // The fact that this succeeds means the parameters were handled correctly
    } catch {
      XCTFail("Get collections with custom parameters should succeed, but failed with error: \(error)")
    }
  }

  func testGetCollectionsUnauthorized() async {
    // Given
    mockUserState.mockIsSignedIn = false
    mockUserState.mockToken = nil

    // When/Then
    do {
      _ = try await collectionService.getCollections()
      XCTFail("Get collections should fail when not authenticated")
    } catch let error as CollectionServiceError {
      XCTAssertEqual(error, .notAuthenticated)
    } catch {
      XCTFail("Unexpected error type: \(error)")
    }
  }

  func testCreateEmptyCollectionSuccess() async {
    // Given
    mockUserState.mockIsSignedIn = true
    mockUserState.mockToken = "valid-token"

    let mockCreatedCollectionJSON = """
    {
        "id": 2,
        "name": "New Test Collection",
        "thumbnail": "https://example.com/new-thumbnail.jpg",
        "tracks": [],
        "created": "2024-01-02T00:00:00Z",
        "updated": "2024-01-02T00:00:00Z"
    }
    """
    mockClient.mockResponse = mockCreatedCollectionJSON.data(using: .utf8)

    // When
    do {
      let createdCollection = try await collectionService.createEmptyCollection(
        name: "New Test Collection",
        thumbnail: "https://example.com/new-thumbnail.jpg"
      )

      // Then
      XCTAssertEqual(mockClient.capturedPath, "/api/v1/collection")
      XCTAssertEqual(createdCollection.name, "New Test Collection")
      XCTAssertEqual(createdCollection.id, 2)
      XCTAssertEqual(createdCollection.tracks.count, 0)

      // Verify request body by parsing the JSON instead of string matching
      if let bodyData = mockClient.capturedBody {
        do {
          let json = try JSONSerialization.jsonObject(with: bodyData) as? [String: Any]
          XCTAssertEqual(json?["name"] as? String, "New Test Collection")
          XCTAssertEqual(json?["thumbnail"] as? String, "https://example.com/new-thumbnail.jpg")
        } catch {
          XCTFail("Could not parse request body as JSON: \(error)")
        }
      } else {
        XCTFail("No request body was captured")
      }
    } catch {
      XCTFail("Create empty collection should succeed, but failed with error: \(error)")
    }
  }

  func testCreateEmptyCollectionWithoutThumbnail() async {
    // Given
    mockUserState.mockIsSignedIn = true
    mockUserState.mockToken = "valid-token"

    let mockCreatedCollectionJSON = """
    {
        "id": 3,
        "name": "Collection Without Thumbnail",
        "thumbnail": "",
        "tracks": [],
        "created": "2024-01-02T00:00:00Z",
        "updated": "2024-01-02T00:00:00Z"
    }
    """
    mockClient.mockResponse = mockCreatedCollectionJSON.data(using: .utf8)

    // When
    do {
      let createdCollection = try await collectionService.createEmptyCollection(
        name: "Collection Without Thumbnail"
      )

      // Then
      XCTAssertEqual(createdCollection.name, "Collection Without Thumbnail")
      XCTAssertEqual(createdCollection.thumbnail, "")
    } catch {
      XCTFail("Create empty collection without thumbnail should succeed, but failed with error: \(error)")
    }
  }

  func testCreateEmptyCollectionUnauthorized() async {
    // Given
    mockUserState.mockIsSignedIn = false
    mockUserState.mockToken = nil

    // When/Then
    do {
      _ = try await collectionService.createEmptyCollection(name: "Test Collection")
      XCTFail("Create empty collection should fail when not authenticated")
    } catch let error as CollectionServiceError {
      XCTAssertEqual(error, .notAuthenticated)
    } catch {
      XCTFail("Unexpected error type: \(error)")
    }
  }

  func testAddTrackToCollectionSuccess() async {
    // Given
    mockUserState.mockIsSignedIn = true
    mockUserState.mockToken = "valid-token"

    let mockResponseJSON = """
    {
        "id": 1,
        "name": "Test Collection",
        "thumbnail": "https://example.com/thumbnail.jpg",
        "tracks": [
            {
                "id": 101,
                "trackTitle": "Test Track",
                "artistName": "Test Artist",
                "url": "https://example.com/track",
                "created": "2024-01-01T00:00:00Z",
                "updated": "2024-01-01T00:00:00Z"
            }
        ],
        "created": "2024-01-01T00:00:00Z",
        "updated": "2024-01-01T00:00:00Z"
    }
    """
    mockClient.mockResponse = mockResponseJSON.data(using: .utf8)

    // When
    do {
      let result = try await collectionService.addTrackToCollection(
        collectionServerId: 1,
        trackServerId: 101
      )

      // Then
      XCTAssertTrue(result)
      XCTAssertEqual(mockClient.capturedPath, "/api/v1/collection/1/add")

      // Verify request body contains the track ID
      if let bodyData = mockClient.capturedBody,
         let bodyString = String(data: bodyData, encoding: .utf8) {
        XCTAssertTrue(bodyString.contains("101"))
      }
    } catch {
      XCTFail("Add track to collection should succeed, but failed with error: \(error)")
    }
  }

  func testRemoveTrackFromCollectionSuccess() async {
    // Given
    mockUserState.mockIsSignedIn = true
    mockUserState.mockToken = "valid-token"

    let mockResponseJSON = """
    {
        "id": 1,
        "name": "Test Collection",
        "thumbnail": "https://example.com/thumbnail.jpg",
        "tracks": [],
        "created": "2024-01-01T00:00:00Z",
        "updated": "2024-01-01T00:00:00Z"
    }
    """
    mockClient.mockResponse = mockResponseJSON.data(using: .utf8)

    // When
    do {
      let result = try await collectionService.removeTrackFromCollection(
        collectionServerId: 1,
        trackServerId: 101
      )

      // Then
      XCTAssertTrue(result)
      XCTAssertEqual(mockClient.capturedPath, "/api/v1/collection/1/remove")
    } catch {
      XCTFail("Remove track from collection should succeed, but failed with error: \(error)")
    }
  }

  func testDeleteCollectionSuccess() async {
    // Given
    mockUserState.mockIsSignedIn = true
    mockUserState.mockToken = "valid-token"

    // DELETE endpoint returns raw Data, so we can use empty data
    mockClient.mockResponse = Data()

    // When
    do {
      let result = try await collectionService.deleteCollection(serverId: 1)

      // Then
      XCTAssertTrue(result)
      XCTAssertEqual(mockClient.capturedPath, "/api/v1/collection/1")
    } catch {
      XCTFail("Delete collection should succeed, but failed with error: \(error)")
    }
  }

  func testDeleteCollectionUnauthorized() async {
    // Given
    mockUserState.mockIsSignedIn = false
    mockUserState.mockToken = nil

    // When/Then
    do {
      _ = try await collectionService.deleteCollection(serverId: 1)
      XCTFail("Delete collection should fail when not authenticated")
    } catch let error as CollectionServiceError {
      XCTAssertEqual(error, .notAuthenticated)
    } catch {
      XCTFail("Unexpected error type: \(error)")
    }
  }
}
