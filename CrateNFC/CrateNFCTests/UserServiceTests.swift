import Foundation
import SwiftData
import XCTest

@testable import CrateServices

final class UserServiceTests: XCTestCase {
  // MARK: - Mocks

  class MockHTTPClient: HTTPClient {
    var mockResponse: Data?
    var mockError: Error?
    var capturedPath: String?
    var capturedBody: Data?
    var capturedParameters: Any?
    var capturedToken: String?
    var tokenRemoved = false

    override init(baseURL: String = "http://localhost:8000") {
      super.init(baseURL: baseURL)
      setApiKey("crate2025")
    }

    override public func setBearerToken(_ token: String) {
      super.setBearerToken(token)
      capturedToken = token
    }

    override public func removeBearerToken() {
      super.removeBearerToken()
      tokenRemoved = true
    }

    // Override the correct method signatures with parameters
    override public func get<Parameters: Encodable, Response: Decodable>(
      path: String,
      parameters: Parameters? = nil
    ) async throws -> Response {
      capturedPath = path
      capturedParameters = parameters

      if let error = mockError {
        throw error
      }

      guard let responseData = mockResponse else {
        throw HTTPError.invalidResponse
      }

      return try JSONDecoder().decode(Response.self, from: responseData)
    }

    override public func post<RequestBody: Encodable, Response: Decodable>(
      path: String,
      body: RequestBody? = nil
    ) async throws -> Response {
      capturedPath = path
      if let body = body {
        capturedBody = try? JSONEncoder().encode(body)
      }

      if let error = mockError {
        throw error
      }

      guard let responseData = mockResponse else {
        throw HTTPError.invalidResponse
      }

      return try JSONDecoder().decode(Response.self, from: responseData)
    }

    override public func delete<RequestBody: Encodable>(
      path: String,
      body: RequestBody? = nil
    ) async throws -> Data {
      capturedPath = path
      if let body = body {
        capturedBody = try? JSONEncoder().encode(body)
      }

      if let error = mockError {
        throw error
      }

      guard let responseData = mockResponse else {
        throw HTTPError.invalidResponse
      }

      return responseData
    }
  }

  class MockUserState: UserState {
    var mockIsSignedIn = false
    var mockToken: String?
    var logoutCalled = false
    var loginUser: User?
    var loginToken: String?

    override var isSignedIn: Bool {
      return mockIsSignedIn
    }

    override var token: String? {
      get { return mockToken }
      set { mockToken = newValue }
    }

    override var currentUser: User? {
      get { return super.currentUser }
      set { super.currentUser = newValue }
    }

    override func login(user: User, token: String) {
      loginUser = user
      loginToken = token
      mockIsSignedIn = true
      mockToken = token
      currentUser = user
    }

    override func logout() {
      logoutCalled = true
      mockIsSignedIn = false
      mockToken = nil
      currentUser = nil
    }
  }

  // MARK: - Test Setup

  var mockClient: MockHTTPClient!
  var mockUserState: MockUserState!
  var userService: UserService!

  override func setUp() {
    super.setUp()
    mockClient = MockHTTPClient()
    mockUserState = MockUserState()
    userService = UserService(client: mockClient, userState: mockUserState)
  }

  override func tearDown() {
    mockClient = nil
    mockUserState = nil
    userService = nil
    super.tearDown()
  }

  // MARK: - Test Cases

  func testLoginSuccess() async {
    // Note: This test is challenging because UserService uses MSAL authentication
    // which involves delegate methods and continuations.
    // For a proper test, you would need to mock the MSAL components or
    // create a testable version of UserService that doesn't use MSAL.

    // For now, we'll skip this test or create a simplified version
    // that tests the core logic without MSAL
  }

  func testLoginFailure() async {
    // Similar to testLoginSuccess, this would require mocking MSAL
    // Skip for now or implement with MSAL mocking
  }

  func testLogout() {
    // When
    userService.logout()

    // Then
    XCTAssertTrue(mockUserState.logoutCalled)
    XCTAssertTrue(mockClient.tokenRemoved)
  }

}
