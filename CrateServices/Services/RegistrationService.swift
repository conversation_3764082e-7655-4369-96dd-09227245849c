import Foundation
import MSAL
import SwiftData

public protocol RegistrationServiceProtocol {
  func startRegistration(email: String, password: String) async throws -> RegistrationResult
  func submitVerificationCode(_ code: String) async throws -> UserDTO
  func resendVerificationCode() async throws
}

public enum RegistrationResult {
  case verificationRequired(sentTo: String)
  case completed(UserDTO)
}

public enum RegistrationServiceError: Error {
  case emailAlreadyExists
  case invalidPassword
  case invalidVerificationCode
  case registrationFailed
  case verificationRequired
  case noActiveRegistration
}

// we declared this already in the UserService
// class Configuration: NSObject {
//   // Update the below to your client ID and tenantSubdomain you received in the portal.
//   static let clientId = "6d030d81-5d4a-42d1-a4c8-76c03b13fcc4"
//   static let tenantSubdomain = "cratenfc"
// }

public class RegistrationService: RegistrationServiceProtocol {
  private let client: HTTPClient
  private let userState: UserState
  private let authPublisher = AuthPublisher.shared
  private var nativeAuth: MSALNativeAuthPublicClientApplication!
  private let userService: UserServiceProtocol

  // Registration state management
  private var registrationContinuation: CheckedContinuation<RegistrationResult, Error>?
  private var verificationContinuation: CheckedContinuation<UserDTO, Error>?
  private var signUpCodeRequiredState: SignUpCodeRequiredState?

  public init(
    client: HTTPClient, userState: UserState = UserState.shared, userService: UserServiceProtocol
  ) {
    self.client = client
    self.userState = userState
    self.userService = userService

    // Access the MSAL instance from UserService instead of creating a new one
    if let userServiceImpl = userService as? UserService,
       let sharedMSAL = userServiceImpl.getMSALInstance() {
      nativeAuth = sharedMSAL
      print("Initialized RegistrationService using shared MSAL instance")
    } else {
      // Fallback: create our own instance
      do {
        nativeAuth = try MSALNativeAuthPublicClientApplication(
          clientId: Configuration.clientId,
          tenantSubdomain: Configuration.tenantSubdomain,
          challengeTypes: [.OOB, .password]
        )
        print("Initialized MSALNativeAuthPublicClientApplication for Registration (fallback)")
      } catch {
        print("Unable to initialize MSAL for Registration: \(error)")
      }
    }
  }

  public func startRegistration(email: String, password: String) async throws
  -> RegistrationResult {
    return try await withCheckedThrowingContinuation { continuation in
      self.registrationContinuation = continuation

      let parameters = MSALNativeAuthSignUpParameters(username: email)
      parameters.password = password

      nativeAuth.signUp(parameters: parameters, delegate: self)
    }
  }

  public func submitVerificationCode(_ code: String) async throws -> UserDTO {
    print("🔍 Submitting verification code: '\(code)'")
    print("🔍 Code length: \(code.count)")
    print("🔍 signUpCodeRequiredState exists: \(signUpCodeRequiredState != nil)")

    guard let signUpState = signUpCodeRequiredState else {
      print("❌ No active registration state!")
      throw RegistrationServiceError.noActiveRegistration
    }

    print("🔄 About to call submitCode on MSAL")
    return try await withCheckedThrowingContinuation { continuation in
      self.verificationContinuation = continuation
      signUpState.submitCode(code: code, delegate: self)
    }
  }

  public func resendVerificationCode() async throws {
    guard let signUpState = signUpCodeRequiredState else {
      throw RegistrationServiceError.noActiveRegistration
    }

    signUpState.resendCode(delegate: self)
  }

  private func showResultText(_ text: String) {
    print(text)
  }
}

// MARK: - SignUpStartDelegate

extension RegistrationService: SignUpStartDelegate {
  public func onSignUpStartError(error: MSAL.SignUpStartError) {
    showResultText("SignUpStartDelegate: onSignUpStartError: \(error)")

    if let continuation = registrationContinuation {
      if error.isUserAlreadyExists {
        showResultText("Unable to sign up: User already exists")
        continuation.resume(throwing: RegistrationServiceError.emailAlreadyExists)
      } else if error.isInvalidPassword {
        showResultText("Unable to sign up: The password is invalid")
        continuation.resume(throwing: RegistrationServiceError.invalidPassword)
      } else {
        showResultText(
          "Unexpected error signing up: \(error.errorDescription ?? "No error description")")
        continuation.resume(throwing: RegistrationServiceError.registrationFailed)
      }
      registrationContinuation = nil
    }
  }

  public func onSignUpCodeRequired(
    newState: MSAL.SignUpCodeRequiredState,
    sentTo: String,
    channelTargetType _: MSAL.MSALNativeAuthChannelType,
    codeLength _: Int
  ) {
    showResultText("SignUpStartDelegate: onSignUpCodeRequired")

    // Store the state for verification
    signUpCodeRequiredState = newState

    // Resume with verification required result
    if let continuation = registrationContinuation {
      continuation.resume(returning: .verificationRequired(sentTo: sentTo))
      registrationContinuation = nil
    }
  }
}

// MARK: - SignUpVerifyCodeDelegate

extension RegistrationService: SignUpVerifyCodeDelegate {
  public func onSignUpVerifyCodeError(
    error: MSAL.VerifyCodeError, newState: MSAL.SignUpCodeRequiredState?
  ) {
    print("❌ MSAL Verification Error Details:")
    print("❌ Error: \(error)")
    print("❌ Error description: \(error.errorDescription ?? "No description")")
    print("❌ Is invalid code: \(error.isInvalidCode)")
    print("❌ New state provided: \(newState != nil)")
    showResultText("SignUpVerifyCodeDelegate: onSignUpVerifyCodeError: \(error)")

    if let continuation = verificationContinuation {
      if error.isInvalidCode {
        showResultText("Invalid verification code")
        continuation.resume(throwing: RegistrationServiceError.invalidVerificationCode)
      } else {
        showResultText(
          "Unexpected error verifying code: \(error.errorDescription ?? "No error description")")
        continuation.resume(throwing: RegistrationServiceError.registrationFailed)
      }
      verificationContinuation = nil
    }

    // Update state if provided
    if let newState = newState {
      signUpCodeRequiredState = newState
    }
  }

  public func onSignUpCompleted(newState: MSAL.SignInAfterSignUpState) {
    showResultText("Signed up successfully!")

    // Automatically sign in after successful registration
    let parameters = MSALNativeAuthSignInAfterSignUpParameters()
    newState.signIn(parameters: parameters, delegate: self)
  }
}

// MARK: - SignUpResendCodeDelegate

extension RegistrationService: SignUpResendCodeDelegate {
  public func onSignUpResendCodeError(
    error: MSAL.ResendCodeError, newState _: MSAL.SignUpCodeRequiredState?
  ) {
    showResultText("SignUpResendCodeDelegate: onSignUpResendCodeError: \(error)")
    // For resend errors, we don't need to resume any continuation
    // The UI can handle this as needed
  }

  public func onSignUpResendCodeCodeRequired(
    newState: MSAL.SignUpCodeRequiredState,
    sentTo _: String,
    channelTargetType _: MSAL.MSALNativeAuthChannelType,
    codeLength _: Int
  ) {
    showResultText("Code resent successfully")
    signUpCodeRequiredState = newState
  }
}

// MARK: - SignInAfterSignUpDelegate

extension RegistrationService: SignInAfterSignUpDelegate {
  public func onSignInAfterSignUpError(error: MSAL.SignInAfterSignUpError) {
    print("❌ SignInAfterSignUpError details:")
    print("❌ Error: \(error)")
    print("❌ Error description: \(error.errorDescription ?? "No description")")
    print("❌ Error localizedDescription: \(error.localizedDescription)")

    showResultText(
      "Error signing in after signing up: \(error.errorDescription ?? "Unknown error")")

    if let continuation = verificationContinuation {
      continuation.resume(throwing: RegistrationServiceError.registrationFailed)
      verificationContinuation = nil
    }
  }

  public func onSignInCompleted(result: MSAL.MSALNativeAuthUserAccountResult) {
    print("✅ SignInAfterSignUp completed successfully")
    print("🔍 UserAccountResult: \(result)")
    print("🔍 Account ID: \(result.account.username ?? "nil")")
    print("🔍 Scopes requested: \(["api://cratenfc/access"])")
    showResultText("Sign in after sign up completed")
    print(
      "Try using the LoginCachedMSALAccount instead of direct calling getAccessToken on result of signin"
    )
    useAccountToacquireTokenSilentlyWithouCredentialsDelegate(account: result.account)
  }

  public func useAccountToacquireTokenSilentlyWithouCredentialsDelegate(account: MSALAccount) {
    print("Starting acquireTokenSilent")
    let silentParameters = MSALSilentTokenParameters.init(
      scopes: ["api://cratenfc/access"], account: account)
    nativeAuth.acquireTokenSilent(with: silentParameters) { (msalTokenResult, error) in
      if let error = error {
        // Handle the error (e.g., print it, show an alert, try interactive login)
        print("🔴 MSAL acquireTokenSilent FAILED: \(error.localizedDescription)")
        // You might need to check error.domain and error.code for specific MSAL errors
        // like MSALErrorInteractionRequired
        return
      }

      if let tokenResult = msalTokenResult {
        print("✅ MSAL acquireTokenSilent SUCCEEDED!")
        print("🔑 Access Token: \(tokenResult.accessToken)")
        self.applyAccessTokenResult(result: tokenResult)
      } else {
        print("⚠️ MSAL acquireTokenSilent: No token result and no error.")
      }
    }
  }

  public func applyAccessTokenResult(result: MSALResult) {
    print("🔑 Access Token Retrieved: \(result.accessToken)")
    print("🔑 Setting bearer token on HTTP client...")

    client.setBearerToken(result.accessToken)

    Task { @MainActor in
      do {
        print("📡 Fetching user profile after registration...")

        // Fetch user profile after successful registration
        let profileResponse: ProfileResponse = try await client.get(
          path: "/api/v1/user/profile",
          parameters: nil as EmptyParameters?
        )

        print("👤 Profile Response: \(profileResponse)")

        let userDTO = profileResponse.toUserDTO()
        let user = userDTO.toModel()
        print("👤 Converted User: \(user)")
        print("🔐 Calling userState.login()...")

        // Call login on main actor since UserState is @MainActor
        userState.login(user: user, token: result.accessToken)

        // Wait a moment for the async login to complete, then check state
        await MainActor.run {
          print("🔍 UserState after login:")
          print("🔍 - currentUser email: \(userState.currentUser?.email ?? "none")")
          print("🔍 - token exists: \(userState.token != nil)")
          print("🔍 - isSignedIn: \(userState.isSignedIn)")
        }

        print("📢 Publishing user created event...")
        // Publish registration completion
        authPublisher.publishUserCreated(user: user)

        print("✅ About to resume verification continuation...")
        // Resume verification continuation with success
        if let continuation = self.verificationContinuation {
          continuation.resume(returning: userDTO)
          self.verificationContinuation = nil
          print("✅ Verification continuation resumed successfully")
        } else {
          print("⚠️ No verification continuation found!")
        }

        // Clear registration state
        self.signUpCodeRequiredState = nil
        print("🧹 Registration state cleared")

      } catch {
        print("❌ Error fetching profile after registration: \(error)")

        if let continuation = self.verificationContinuation {
          continuation.resume(throwing: RegistrationServiceError.registrationFailed)
          self.verificationContinuation = nil
        }
      }
    }
  }
}

// MARK: - CredentialsDelegate

extension RegistrationService: CredentialsDelegate {
  public func onAccessTokenRetrieveCompleted(result: MSALNativeAuthTokenResult) {
    print("🔑 Access Token Retrieved: \(result.accessToken)")
    print("🔑 Setting bearer token on HTTP client...")

    client.setBearerToken(result.accessToken)

    Task { @MainActor in
      do {
        print("📡 Fetching user profile after registration...")

        // Fetch user profile after successful registration
        let profileResponse: ProfileResponse = try await client.get(
          path: "/api/v1/user/profile",
          parameters: nil as EmptyParameters?
        )

        print("👤 Profile Response: \(profileResponse)")

        let userDTO = profileResponse.toUserDTO()
        let user = userDTO.toModel()
        print("👤 Converted User: \(user)")
        print("🔐 Calling userState.login()...")

        // Call login on main actor since UserState is @MainActor
        userState.login(user: user, token: result.accessToken)

        // Wait a moment for the async login to complete, then check state
        await MainActor.run {
          print("🔍 UserState after login:")
          print("🔍 - currentUser email: \(userState.currentUser?.email ?? "none")")
          print("🔍 - token exists: \(userState.token != nil)")
          print("🔍 - isSignedIn: \(userState.isSignedIn)")
        }

        print("📢 Publishing user created event...")
        // Publish registration completion
        authPublisher.publishUserCreated(user: user)

        print("✅ About to resume verification continuation...")
        // Resume verification continuation with success
        if let continuation = self.verificationContinuation {
          continuation.resume(returning: userDTO)
          self.verificationContinuation = nil
          print("✅ Verification continuation resumed successfully")
        } else {
          print("⚠️ No verification continuation found!")
        }

        // Clear registration state
        self.signUpCodeRequiredState = nil
        print("🧹 Registration state cleared")

      } catch {
        print("❌ Error fetching profile after registration: \(error)")

        if let continuation = self.verificationContinuation {
          continuation.resume(throwing: RegistrationServiceError.registrationFailed)
          self.verificationContinuation = nil
        }
      }
    }
  }

  public func onAccessTokenRetrieveError(error: MSAL.RetrieveAccessTokenError) {
    print("❌ Access token retrieval failed!")
    print("❌ Error: \(error)")
    print("❌ Error description: \(error.errorDescription ?? "No description")")

    showResultText(
      "Error retrieving access token: \(error.errorDescription ?? "No error description")")

    if let continuation = verificationContinuation {
      continuation.resume(throwing: RegistrationServiceError.registrationFailed)
      verificationContinuation = nil
    }
  }
}

// MARK: - Supporting Types (these might already exist in your UserService)

private struct EmptyParameters: Encodable {}

private struct ProfileResponse: Codable {
  let id: String
  let email: String
  let username: String
  let lastLogin: String
  let entraSubjectId: String
  let created: String?
  let updated: String?

  enum CodingKeys: String, CodingKey {
    case id, email, username, lastLogin, entraSubjectId, created, updated
  }

  func toUserDTO() -> UserDTO {
    let dateFormatter = ISO8601DateFormatter()
    let createdDate = created.flatMap { dateFormatter.date(from: $0) }
    let updatedDate = updated.flatMap { dateFormatter.date(from: $0) }

    return UserDTO(
      email: email,
      username: username,
      updatedAt: updatedDate,
      createdAt: createdDate
    )
  }
}
