import Foundation
import SwiftData

public protocol TrackServiceProtocol {
  // Track endpoints
  func getRecent(start: Int, size: Int) async throws -> [TrackDTO]
  func getSingleRecent() async throws -> TrackDTO?
  func unfurl(url: String) async throws
  func deleteAllTracks() async throws -> Bool
  func deleteTrack(id: Int) async throws -> Bool

  // Trending endpoint
  func getTrending() async throws -> [TrendingTrackDTO]
  func fetchAndSaveTrending(context: ModelContext) async throws -> [TrendingTrackDTO]
  func getAllTrendingTracks(context: ModelContext) throws -> [TrendingTrack]
  func deleteAllTrendingTracks(context: ModelContext) throws
  func saveTrendingTracks(_ tracks: [TrendingTrack], context: ModelContext) throws

  // Recent endpoint
  func saveRecentTracks(_ tracks: [Track], context: ModelContext) throws
  func deleteAllRecentTracks(context: ModelContext) throws

  // Lookup methods
  func getTrackByServerId(_ serverId: Int, context: ModelContext) throws -> Track?
  func getTrendingTrackByServerId(_ serverId: Int, context: ModelContext) throws -> TrendingTrack?
}

public struct TrackService: TrackServiceProtocol {
  private let client: HTTPClient
  private let userState: UserState

  public init(client: HTTPClient, userState: UserState = UserState.shared) {
    self.client = client
    self.userState = userState
  }

  // Server-side Track model
  public struct ServerTrack: Codable {
    let id: Int
    let trackTitle: String
    let artistName: String
    let url: String
    let mediaUrl: String?
    let domainUrl: String?
    let created: String
    let updated: String
  }

  // MARK: - Trending endpoint with TrendingTrack model

  public func getTrending() async throws -> [TrendingTrackDTO] {
    let isoFormatter = ISO8601DateFormatter()

    struct TrendingParams: Codable {
      let start: Int
      let size: Int
    }

    let params = TrendingParams(start: 0, size: 20)
    let tracks: [ServerTrack] = try await client.get(path: "/api/v1/track/trending", parameters: params)

    return tracks.map { track in
      TrendingTrackDTO(
        serverId: track.id,
        name: track.trackTitle,
        artist: track.artistName,
        imgUrl: track.mediaUrl,
        url: track.url,
        domain: track.domainUrl,
        updatedAt: isoFormatter.date(from: track.updated) ?? Date.distantPast,
        createdAt: isoFormatter.date(from: track.created) ?? Date.distantPast
      )
    }
  }

  public func fetchAndSaveTrending(context: ModelContext) async throws -> [TrendingTrackDTO] {
    let tracks = try await getTrending()

    let models = tracks.map { dto in
      TrendingTrack(
        serverId: dto.serverId,
        name: dto.name,
        artist: dto.artist,
        imgUrl: dto.imgUrl,
        url: dto.url,
        domain: dto.domain,
        updatedAt: dto.updatedAt ?? Date.distantPast,
        createdAt: dto.createdAt ?? Date.distantPast
      )
    }

    try saveTrendingTracks(models, context: context)
    return tracks
  }

  public func getAllTrendingTracks(context: ModelContext) throws -> [TrendingTrack] {
    var fetchDescriptor = FetchDescriptor<TrendingTrack>()
    fetchDescriptor.sortBy = [SortDescriptor(\TrendingTrack.createdAt, order: .reverse)]
    fetchDescriptor.fetchLimit = 20
    let existingTracks = try context.fetch(fetchDescriptor)

    return existingTracks.uniqued(on: { track in
      "\(track.artist ?? ""):\(track.name ?? "")"
    })
  }

  public func getTrendingTrackByServerId(_ serverId: Int, context: ModelContext) throws -> TrendingTrack? {
    let predicate = #Predicate<TrendingTrack> { track in
      track.serverId == serverId
    }

    let descriptor = FetchDescriptor<TrendingTrack>(predicate: predicate)
    let tracks = try context.fetch(descriptor)
    return tracks.first
  }

  public func deleteAllTrendingTracks(context: ModelContext) throws {
    try context.delete(model: TrendingTrack.self)
    try context.save()
  }

  public func saveTrendingTracks(_ tracks: [TrendingTrack], context: ModelContext) throws {
    // Check for existing tracks with the same serverId and update them instead of inserting duplicates
    for track in tracks {
      if let serverId = track.serverId, let existingTrack = try? getTrendingTrackByServerId(serverId, context: context) {
        // Update existing track properties
        existingTrack.name = track.name
        existingTrack.artist = track.artist
        existingTrack.imgUrl = track.imgUrl
        existingTrack.url = track.url
        existingTrack.domain = track.domain
        existingTrack.updatedAt = track.updatedAt
      } else {
        // Insert new track
        context.insert(track)
      }
    }

    try context.save()
  }

  // MARK: - Server-side Unfurl endpoint

  public struct UrlRequest: Codable {
    let url: String
  }

  public func unfurl(url: String) async throws {
    let request = UrlRequest(url: url)
    if userState.isSignedIn {
      try await client.postWithoutResponse(
        path: "/api/v1/unfurl",
        body: request
      )
    } else {
      try await client.postWithoutResponse(
        path: "/api/v1/unfurl/anonymous",
        body: request
      )
    }
  }

  // MARK: - Recent endpoint with Track model

  public func getRecent(start: Int = 0, size: Int = 20) async throws -> [TrackDTO] {
    let isoFormatter = ISO8601DateFormatter()

    struct RecentParams: Codable {
      let start: Int
      let size: Int
    }

    let params = RecentParams(start: start, size: size)
    let tracks: [ServerTrack] = try await client.get(
      path: "/api/v1/track/recent",
      parameters: params
    )

    return tracks.map { track in
      let domain = URL(string: track.url)?.host

      return TrackDTO(
        serverId: track.id,
        name: track.trackTitle,
        artist: track.artistName,
        imgUrl: track.mediaUrl,
        url: track.url,
        domain: domain,
        recentCollections: nil,
        collections: nil,
        updatedAt: isoFormatter.date(from: track.updated) ?? Date.distantPast,
        createdAt: isoFormatter.date(from: track.created) ?? Date.distantPast
      )
    }
  }

  public func getSingleRecent() async throws -> TrackDTO? {
    let isoFormatter = ISO8601DateFormatter()

    struct RecentParams: Codable {
      let start: Int
      let size: Int
    }

    let params = RecentParams(start: 0, size: 1)
    let tracks: [ServerTrack] = try await client.get(
      path: "/api/v1/track/recent",
      parameters: params
    )

    return tracks.first.map { track in
      TrackDTO(
        serverId: track.id,
        name: track.trackTitle,
        artist: track.artistName,
        imgUrl: track.mediaUrl,
        url: track.url,
        domain: track.domainUrl,
        recentCollections: nil,
        collections: nil,
        updatedAt: isoFormatter.date(from: track.updated) ?? Date.distantPast,
        createdAt: isoFormatter.date(from: track.created) ?? Date.distantPast
      )
    }
  }

  // MARK: - Delete Track endpoints

  /// Deletes all tracks for the user
  /// DELETE /api/v1/track
  public func deleteAllTracks() async throws -> Bool {
    guard userState.isSignedIn, userState.token != nil else {
      throw TrackServiceError.notAuthenticated
    }

    do {
      _ = try await client.delete(path: "/api/v1/track", body: EmptyParameters())
      print("✅ Successfully deleted all tracks on the server")
      return true
    } catch {
      print("❌ Error deleting all tracks: \(error.localizedDescription)")
      throw TrackServiceError.deleteTracksFailed
    }
  }

  /// Deletes a specific track by ID for the user
  /// DELETE /api/v1/track/{id}
  public func deleteTrack(id: Int) async throws -> Bool {
    guard userState.isSignedIn, userState.token != nil else {
      throw TrackServiceError.notAuthenticated
    }

    do {
      _ = try await client.delete(path: "/api/v1/track/\(id)", body: EmptyParameters())
      print("✅ Successfully deleted track with ID \(id) on the server")
      return true
    } catch {
      print("❌ Error deleting track with ID \(id): \(error.localizedDescription)")
      throw TrackServiceError.deleteTrackFailed
    }
  }

  public func getTrackByServerId(_ serverId: Int, context: ModelContext) throws -> Track? {
    let predicate = #Predicate<Track> { track in
      track.serverId == serverId
    }

    let descriptor = FetchDescriptor<Track>(predicate: predicate)
    let tracks = try context.fetch(descriptor)
    return tracks.first
  }

  // MARK: - Track model persistence methods

  public func saveRecentTracks(_ tracks: [Track], context: ModelContext) throws {
    // Check for existing tracks with the same serverId and update them instead of inserting duplicates
    for track in tracks {
      if let serverId = track.serverId, let existingTrack = try? getTrackByServerId(serverId, context: context) {
        // Update existing track properties
        existingTrack.name = track.name
        existingTrack.artist = track.artist
        existingTrack.imgUrl = track.imgUrl
        existingTrack.url = track.url
        existingTrack.domain = track.domain
        existingTrack.updatedAt = track.updatedAt
      } else {
        // Insert new track
        context.insert(track)
      }
    }

    try context.save()
  }

  public func deleteAllRecentTracks(context: ModelContext) throws {
    try context.delete(model: Track.self)
    try context.save()
  }
}

// MARK: - Track Service Errors

public enum TrackServiceError: Error {
  case notAuthenticated
  case deleteTracksFailed
  case deleteTrackFailed
}

// Add this at the top of the file, after the imports
private struct EmptyParameters: Encodable {}
