import Foundation

public protocol ApiServiceProtocol {
  func updateApiKey(_ key: String)
}

public struct ApiService: ApiServiceProtocol {
  private let client: HTTPClient
  private let userState: UserState

  public init(client: HTTPClient, userState: UserState = UserState.shared) {
    self.client = client
    self.userState = userState

    client.setApiKey(UserDefaults.Keys.apiKey)
  }

  public func updateApiKey(_ key: String) {
    client.setApiKey(key)
  }
}
